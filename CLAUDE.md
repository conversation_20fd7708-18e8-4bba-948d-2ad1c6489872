# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

宠物博客多语言站群系统是一个面向全球宠物爱好者的内容平台，通过AI辅助翻译和本地化优化，为不同国家和地区的用户提供高质量的宠物养护知识。

### 核心特性
- **多语言独立架构**：每个语言版本作为独立站点运行，避免i18n性能损耗
- **AI翻译工作流**：集成Gemini AI实现高效内容翻译
- **SEO优先设计**：严格遵循Google 2025最新SEO标准，目标Lighthouse评分>90
- **高性能架构**：静态生成(SSG) + 边缘缓存实现极速访问
- **模块化扩展**：支持灵活添加新语言和功能模块

### 技术栈
- **前端**：Astro 4.0+ (SSG)、Tailwind CSS 3.4+、TypeScript
- **后端**：Node.js 20 LTS、Express 4.19+、TypeScript
- **数据库**：MySQL 9.0.1 (远程)、Redis 7.2+ (本地缓存)
- **AI服务**：Gemini API (翻译服务)
- **部署**：宝塔面板、PM2、Nginx、Let's Encrypt

## 关键配置信息

### 数据库连接（敏感信息，请通过环境变量管理）
```bash
DB_HOST=************
DB_NAME=bengtai
DB_USER=bengtai
DB_PASSWORD=weizhen258  # 敏感信息，生产环境必须使用环境变量
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
```

### Gemini AI API配置（敏感信息）
```bash
GEMINI_API_ENDPOINT=https://ai.wanderintree.top
GEMINI_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d  # 敏感信息
GEMINI_MODEL=gemini-2.5-pro
```

## 常用开发命令

### 项目初始化
```bash
# 克隆项目后首次运行
cd petcare-blog

# 后端初始化
cd backend
npm install
cp .env.example .env  # 配置环境变量
npm run db:migrate    # 运行数据库迁移
npm run db:seed       # 插入初始数据

# 前端初始化
cd ../frontend
npm install
```

### 开发服务器
```bash
# 后端开发服务器（支持热重载）
cd backend
npm run dev          # 启动在 http://localhost:3000

# 前端开发服务器
cd frontend
npm run dev:en       # 英文站点 http://localhost:4321
npm run dev:de       # 德文站点 http://localhost:4322
npm run dev:ru       # 俄文站点 http://localhost:4323
npm run dev:admin    # 管理后台 http://localhost:4324
```

### 构建和部署
```bash
# 后端构建
cd backend
npm run build        # TypeScript编译
npm run start        # 生产环境启动

# 前端构建（所有语言版本）
cd frontend
npm run build:all    # 构建所有语言版本
npm run build:en     # 仅构建英文版
npm run build:de     # 仅构建德文版
npm run build:ru     # 仅构建俄文版
npm run build:admin  # 构建管理后台
```

### 测试命令
```bash
# 后端测试
cd backend
npm test             # 运行所有测试
npm run test:watch   # 监听模式
npm run test:coverage # 测试覆盖率报告

# 前端测试
cd frontend
npm test             # 运行组件测试
npm run test:e2e     # 运行E2E测试
```

### 代码质量检查
```bash
# 在项目根目录运行
npm run lint         # ESLint检查
npm run lint:fix     # 自动修复lint问题
npm run format       # Prettier格式化
npm run typecheck    # TypeScript类型检查
```

### 数据库操作
```bash
cd backend
npm run db:migrate        # 运行最新迁移
npm run db:rollback       # 回滚上一次迁移
npm run db:reset          # 重置数据库（危险）
npm run db:seed           # 插入测试数据
```

## 项目结构说明

### 后端架构 (backend/)
```
backend/
├── src/
│   ├── controllers/      # 控制器层，处理HTTP请求
│   │   ├── auth.controller.ts
│   │   ├── article.controller.ts
│   │   └── translation.controller.ts
│   ├── services/         # 业务逻辑层
│   │   ├── article.service.ts
│   │   ├── translation.service.ts (Gemini AI集成)
│   │   └── cache.service.ts (Redis缓存)
│   ├── models/          # 数据模型（TypeORM实体）
│   │   ├── Article.ts
│   │   ├── ArticleTranslation.ts
│   │   └── User.ts
│   ├── middlewares/     # Express中间件
│   │   ├── auth.middleware.ts (JWT验证)
│   │   ├── error.middleware.ts
│   │   └── rateLimit.middleware.ts
│   ├── routes/          # 路由定义
│   │   └── v1/         # API版本管理
│   ├── utils/          # 工具函数
│   ├── config/         # 配置文件
│   └── types/          # TypeScript类型定义
├── tests/              # 测试文件
├── migrations/         # 数据库迁移文件
└── seeds/             # 数据库种子文件
```

### 前端架构 (frontend/)
```
frontend/
├── en/                 # 英文站点
│   ├── src/
│   │   ├── components/     # Astro组件
│   │   │   ├── Layout.astro
│   │   │   ├── ArticleCard.astro
│   │   │   └── SEOHead.astro
│   │   ├── pages/         # 页面路由
│   │   │   ├── index.astro
│   │   │   ├── [category]/index.astro
│   │   │   └── article/[slug].astro
│   │   ├── styles/        # 样式文件
│   │   └── lib/          # 工具库
│   └── public/           # 静态资源
├── de/                 # 德文站点（结构同上）
├── ru/                 # 俄文站点（结构同上）
└── admin/              # 管理后台（React）
    ├── src/
    │   ├── components/
    │   ├── pages/
    │   ├── services/     # API调用
    │   └── store/        # 状态管理
    └── public/
```

## 核心功能开发指南

### 1. 文章管理流程
```typescript
// 创建文章（中文原稿）
POST /api/v1/articles
{
  "title": "新手养猫指南",
  "content": "<p>详细内容...</p>",
  "category_id": 3,
  "language_code": "zh-CN"
}

// 触发AI翻译
POST /api/v1/articles/{id}/translations
{
  "target_languages": ["en-US", "de-DE", "ru-RU"]
}

// 人工审核翻译
PUT /api/v1/translations/{id}
{
  "title": "Beginner's Guide to Cat Care",
  "content": "<p>Reviewed content...</p>",
  "translation_status": "human_reviewed"
}
```

### 2. 数据库查询优化原则
- 使用TypeORM的QueryBuilder进行复杂查询
- 始终使用索引字段进行查询
- 分页查询必须使用LIMIT和OFFSET
- 关联查询使用leftJoinAndSelect避免N+1问题

### 3. 缓存策略
```typescript
// Redis缓存键规范
const cacheKeys = {
  article: `article:${lang}:${id}`,
  articleList: `articles:${lang}:${category}:${page}`,
  categories: `categories:${lang}`,
  hotArticles: `hot:${lang}:${limit}`
};

// 缓存时间设置
const cacheTTL = {
  article: 3600,      // 1小时
  articleList: 300,   // 5分钟
  categories: 21600,  // 6小时
  hotArticles: 1800   // 30分钟
};
```

### 4. SEO优化要点
- 每个页面必须有唯一的meta title和description
- 使用结构化数据(JSON-LD)标记文章
- 生成XML sitemap并定期更新
- 图片必须有alt属性
- 实现canonical URL避免重复内容

### 5. 性能优化检查清单
- [ ] 启用Gzip/Brotli压缩
- [ ] 配置HTTP/2
- [ ] 实施图片懒加载
- [ ] 使用WebP格式图片
- [ ] 配置CDN加速
- [ ] 启用浏览器缓存
- [ ] 代码分割和按需加载
- [ ] 关键CSS内联

## 开发注意事项

### 安全性
1. **永远不要**在代码中硬编码敏感信息
2. 所有用户输入必须进行验证和清理
3. 使用参数化查询防止SQL注入
4. 实施CSRF保护
5. 配置适当的CORS策略

### 代码规范
1. 使用TypeScript严格模式
2. 遵循ESLint和Prettier配置
3. 函数和变量使用有意义的名称
4. 添加必要的类型注解
5. 编写单元测试覆盖核心功能

### Git工作流
```bash
# 功能开发流程
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# 开发完成后
git add .
git commit -m "feat: 添加XX功能"
git push origin feature/your-feature-name

# 创建Pull Request到develop分支
```

### 部署流程
1. 确保所有测试通过
2. 更新版本号和CHANGELOG
3. 合并到main分支
4. 触发CI/CD自动部署
5. 验证生产环境功能

## 故障排查

### 常见问题
1. **数据库连接失败**
   - 检查远程数据库IP是否可访问
   - 确认用户名密码正确
   - 检查防火墙设置

2. **Redis连接问题**
   - 确认Redis服务已启动
   - 检查端口6379是否被占用
   - 查看Redis日志

3. **API翻译失败**
   - 检查Gemini API密钥是否有效
   - 确认API端点可访问
   - 查看API配额是否用尽

4. **前端构建失败**
   - 清理node_modules重新安装
   - 检查Node.js版本是否正确
   - 查看是否有TypeScript类型错误

### 性能调试
```bash
# 后端性能分析
npm run profile

# 数据库慢查询日志
tail -f /var/log/mysql/slow-query.log

# Redis监控
redis-cli monitor

# PM2进程监控
pm2 monit
```

## 扩展新语言站点

1. 复制现有语言站点目录
2. 修改`astro.config.mjs`中的语言配置
3. 更新`site.config.ts`添加新语言
4. 在数据库`sites`表中添加新站点记录
5. 配置新域名和SSL证书
6. 更新CI/CD配置包含新站点

记住：每个语言站点都是独立部署的，这样可以获得最佳的SEO效果和性能表现。