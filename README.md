# 宠物博客多语言站群系统 (Pet Care Blog Multi-language System)

一个面向全球宠物爱好者的内容平台，通过AI辅助翻译和本地化优化，为不同国家和地区的用户提供高质量的宠物养护知识。

## 🎯 项目特性

- **多语言独立架构**：每个语言版本作为独立站点运行，避免i18n性能损耗
- **AI翻译工作流**：集成Gemini AI实现高效内容翻译
- **SEO优先设计**：严格遵循Google 2025最新SEO标准，目标Lighthouse评分>90
- **高性能架构**：静态生成(SSG) + 边缘缓存实现极速访问
- **模块化扩展**：支持灵活添加新语言和功能模块

## 🛠 技术栈

### 前端
- **框架**: Astro 4.0+ (SSG)
- **样式**: Tailwind CSS 3.4+
- **语言**: TypeScript
- **构建**: Vite

### 后端
- **运行时**: Node.js 20 LTS
- **框架**: Express 4.19+
- **语言**: TypeScript
- **API**: RESTful

### 数据存储
- **数据库**: MySQL 9.0.1 (远程)
- **缓存**: Redis 7.2+ (本地)
- **文件**: 本地存储 + CDN

### 第三方服务
- **AI翻译**: Gemini API
- **部署**: 宝塔面板 + PM2 + Nginx
- **SSL**: Let's Encrypt

## 📋 开发进度

### Phase 1: 基础架构搭建 (10步)
- [x] **Step 1**: 开发环境初始化 ✅ (2025-01-05)
  - [x] Node.js 22.11.0 已安装 (兼容20 LTS)
  - [x] MySQL 客户端已安装 (MariaDB client 15.2)
  - [x] Redis 8.0.3 已安装并运行
  - [x] VS Code 配置完成
  - [x] Git 2.39.5 已配置
  - [x] 开发工具验证完成
  - [x] 远程数据库连接测试成功

- [ ] **Step 2**: 项目结构初始化
- [ ] **Step 3**: Git仓库配置
- [ ] **Step 4**: Docker环境配置
- [ ] **Step 5**: 环境变量配置
- [ ] **Step 6**: 日志系统搭建
- [ ] **Step 7**: 错误处理机制
- [ ] **Step 8**: API文档系统
- [ ] **Step 9**: 测试框架搭建
- [ ] **Step 10**: CI/CD基础配置

### Phase 2-7: 待开发
详见 [开发步骤文档](./docs/08-development-steps.md)

## 🚀 快速开始

### 环境要求
- Node.js >= 20.0.0
- MySQL 客户端
- Redis >= 7.2
- Git

### 环境检查
```bash
# 运行环境检查脚本
npm run check:env

# 测试数据库连接
npm run test:mysql
```

### 开发环境设置
```bash
# 克隆项目
git clone [repository-url]
cd bbyu

# 安装依赖
npm install

# 后续步骤待完成...
```

## 📁 项目结构

```
bbyu/
├── docs/                    # 项目文档
│   ├── 01-project-architecture.md
│   ├── 02-database-design.md
│   ├── 03-api-design.md
│   ├── 04-frontend-development.md
│   ├── 05-deployment-guide.md
│   ├── 06-testing-document.md
│   ├── 07-maintenance-document.md
│   └── 08-development-steps.md
├── scripts/                 # 工具脚本
│   ├── check-dev-env.sh    # 环境检查脚本
│   └── test-mysql-connection.js  # 数据库连接测试
├── .vscode/                # VS Code 配置
│   ├── extensions.json     # 推荐扩展
│   └── settings.json       # 项目设置
├── backend/                # 后端代码 (待创建)
├── frontend/               # 前端代码 (待创建)
├── package.json           # 项目配置
└── README.md             # 本文件
```

## 🔧 开发工具

### VS Code 推荐扩展
项目已配置推荐扩展列表，打开项目后 VS Code 会提示安装。主要包括：
- ESLint / Prettier
- TypeScript 支持
- Astro / Tailwind CSS
- MySQL / Redis 客户端
- Git 增强工具

### 实用脚本
- `npm run check:env` - 检查开发环境
- `npm run test:mysql` - 测试数据库连接

## 📝 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- Git commit 遵循 Conventional Commits
- 代码注释使用中英双语

## 🔐 安全注意事项

- 敏感信息（如数据库密码、API密钥）必须通过环境变量管理
- 不要在代码中硬编码任何敏感信息
- 生产环境必须使用 HTTPS
- 定期更新依赖包

## 📞 联系方式

如有问题，请查阅项目文档或联系开发团队。

---

最后更新：2025-01-05