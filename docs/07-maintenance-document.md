# 宠物博客站群系统 - 维护文档

## 目录
- [1. 日常维护流程](#1-日常维护流程)
- [2. 备份策略](#2-备份策略)
- [3. 监控配置](#3-监控配置)
- [4. 故障处理](#4-故障处理)
- [5. 性能优化](#5-性能优化)
- [6. 安全维护](#6-安全维护)
- [7. 数据库维护](#7-数据库维护)
- [8. 灾难恢复](#8-灾难恢复)

## 1. 日常维护流程

### 1.1 每日检查清单
```yaml
早晨检查 (9:00 AM):
  - [ ] 检查所有站点可访问性
  - [ ] 查看夜间监控告警
  - [ ] 检查磁盘空间使用率
  - [ ] 查看错误日志摘要
  - [ ] 验证备份完成状态
  - [ ] 检查SSL证书有效期
  
系统健康检查:
  - [ ] CPU使用率 < 70%
  - [ ] 内存使用率 < 80%
  - [ ] 磁盘使用率 < 85%
  - [ ] 数据库连接数正常
  - [ ] Redis内存使用正常
  - [ ] API响应时间 < 200ms
```

### 1.2 每周维护任务
```bash
#!/bin/bash
# weekly-maintenance.sh

echo "=== 开始每周维护 $(date) ==="

# 1. 清理日志文件
echo "清理日志文件..."
find /www/wwwlogs -name "*.log" -mtime +30 -delete
find /www/wwwroot/petcare/logs -name "*.log" -mtime +30 -delete

# 2. 优化数据库
echo "优化数据库表..."
mysql -h ************ -u bengtai -p${DB_PASSWORD} bengtai << EOF
OPTIMIZE TABLE articles;
OPTIMIZE TABLE article_translations;
OPTIMIZE TABLE comments;
ANALYZE TABLE articles;
ANALYZE TABLE article_translations;
EOF

# 3. 清理临时文件
echo "清理临时文件..."
find /www/wwwroot/petcare/temp -type f -mtime +7 -delete
find /tmp -name "sess_*" -mtime +7 -delete

# 4. 更新系统包
echo "检查系统更新..."
apt update
apt list --upgradable

# 5. 生成维护报告
echo "生成维护报告..."
/www/server/panel/generate-report.sh > /www/wwwlogs/weekly-report-$(date +%Y%m%d).txt

echo "=== 维护完成 $(date) ==="
```

### 1.3 每月维护任务
```yaml
月度任务清单:
  系统维护:
    - 更新操作系统补丁
    - 更新Node.js依赖包
    - 检查并更新SSL证书
    - 清理Docker镜像和容器
    
  性能分析:
    - 分析慢查询日志
    - 审查API性能报告
    - 检查CDN命中率
    - 评估资源使用趋势
    
  安全审查:
    - 检查系统安全日志
    - 审查用户权限设置
    - 扫描安全漏洞
    - 更新防火墙规则
    
  容量规划:
    - 评估存储使用增长
    - 预测流量增长趋势
    - 计划扩容需求
```

## 2. 备份策略

### 2.1 备份配置
```yaml
备份类型:
  数据库备份:
    频率: 每日凌晨2点
    保留: 30天
    类型: 全量备份 + 增量备份
    
  文件备份:
    频率: 每日凌晨3点
    保留: 14天
    包含: 上传文件、配置文件
    
  代码备份:
    频率: 每次部署
    保留: 永久 (Git)
    
  系统快照:
    频率: 每周日凌晨4点
    保留: 4周
```

### 2.2 数据库备份脚本
```bash
#!/bin/bash
# backup-database.sh

# 配置
BACKUP_DIR="/backup/mysql"
DB_HOST="************"
DB_NAME="bengtai"
DB_USER="bengtai"
DB_PASS="weizhen258"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行备份
echo "开始数据库备份: ${DATE}"

# 全量备份
mysqldump -h ${DB_HOST} \
  -u ${DB_USER} \
  -p${DB_PASS} \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --add-drop-table \
  --complete-insert \
  --extended-insert \
  --lock-tables=false \
  ${DB_NAME} | gzip > ${BACKUP_DIR}/db_full_${DATE}.sql.gz

# 检查备份结果
if [ $? -eq 0 ]; then
    echo "备份成功: db_full_${DATE}.sql.gz"
    
    # 验证备份文件
    gunzip -t ${BACKUP_DIR}/db_full_${DATE}.sql.gz
    if [ $? -eq 0 ]; then
        echo "备份文件验证成功"
        
        # 上传到远程存储
        aws s3 cp ${BACKUP_DIR}/db_full_${DATE}.sql.gz \
          s3://petcare-backups/mysql/db_full_${DATE}.sql.gz
    else
        echo "备份文件损坏！"
        exit 1
    fi
else
    echo "备份失败！"
    exit 1
fi

# 清理旧备份
echo "清理超过 ${RETENTION_DAYS} 天的备份..."
find ${BACKUP_DIR} -name "db_full_*.sql.gz" -mtime +${RETENTION_DAYS} -delete

# 记录备份信息
echo "${DATE},db_full_${DATE}.sql.gz,$(du -h ${BACKUP_DIR}/db_full_${DATE}.sql.gz | cut -f1),success" \
  >> ${BACKUP_DIR}/backup.log
```

### 2.3 文件备份脚本
```bash
#!/bin/bash
# backup-files.sh

BACKUP_DIR="/backup/files"
SOURCE_DIRS=(
    "/www/wwwroot/petcare/uploads"
    "/www/wwwroot/petcare/backend/.env"
    "/www/server/panel/vhost"
)
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=14

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 创建备份
echo "开始文件备份: ${DATE}"

# 使用tar创建压缩备份
tar -czf ${BACKUP_DIR}/files_${DATE}.tar.gz \
    --exclude='*.log' \
    --exclude='node_modules' \
    --exclude='cache' \
    ${SOURCE_DIRS[@]} 2>/dev/null

if [ $? -eq 0 ]; then
    echo "文件备份成功: files_${DATE}.tar.gz"
    
    # 上传到远程存储
    aws s3 cp ${BACKUP_DIR}/files_${DATE}.tar.gz \
      s3://petcare-backups/files/files_${DATE}.tar.gz
    
    # 清理旧备份
    find ${BACKUP_DIR} -name "files_*.tar.gz" -mtime +${RETENTION_DAYS} -delete
else
    echo "文件备份失败！"
    exit 1
fi
```

### 2.4 自动备份配置 (crontab)
```bash
# 编辑crontab
crontab -e

# 数据库备份 - 每日凌晨2点
0 2 * * * /www/server/panel/backup-database.sh >> /www/wwwlogs/backup.log 2>&1

# 文件备份 - 每日凌晨3点
0 3 * * * /www/server/panel/backup-files.sh >> /www/wwwlogs/backup.log 2>&1

# 备份验证 - 每日早上6点
0 6 * * * /www/server/panel/verify-backups.sh

# 备份报告 - 每周一早上9点
0 9 * * 1 /www/server/panel/backup-report.sh | mail -s "Weekly Backup Report" <EMAIL>
```

## 3. 监控配置

### 3.1 监控架构
```yaml
监控层级:
  基础设施监控:
    - 服务器资源 (CPU、内存、磁盘、网络)
    - 服务状态 (Nginx、Node.js、Redis、MySQL)
    - 系统日志
    
  应用监控:
    - API响应时间
    - 错误率
    - 请求量
    - 业务指标
    
  前端监控:
    - 页面加载时间
    - JS错误
    - 资源加载失败
    - 用户体验指标
    
  安全监控:
    - 异常登录
    - 攻击检测
    - 漏洞扫描
```

### 3.2 Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['localhost:9093']

rule_files:
  - "alerts.yml"

scrape_configs:
  # Node Exporter
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
        labels:
          instance: 'petcare-server'
  
  # MySQL Exporter
  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']
        labels:
          instance: 'petcare-mysql'
  
  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
        labels:
          instance: 'petcare-redis'
  
  # Node.js应用
  - job_name: 'nodejs'
    static_configs:
      - targets: ['localhost:3000']
        labels:
          app: 'petcare-api'
  
  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']
```

### 3.3 告警规则配置
```yaml
# alerts.yml
groups:
  - name: server_alerts
    interval: 30s
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg(rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高CPU使用率 (instance {{ $labels.instance }})"
          description: "CPU使用率超过80% (当前值: {{ $value }}%)"
      
      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高内存使用率 (instance {{ $labels.instance }})"
          description: "内存使用率超过85% (当前值: {{ $value }}%)"
      
      # 磁盘空间告警
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足 (instance {{ $labels.instance }})"
          description: "磁盘使用率超过90% (挂载点: {{ $labels.mountpoint }})"
      
      # 服务宕机告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务宕机 (instance {{ $labels.instance }})"
          description: "{{ $labels.job }} 服务已经宕机超过1分钟"
  
  - name: application_alerts
    interval: 30s
    rules:
      # API响应时间告警
      - alert: SlowAPIResponse
        expr: histogram_quantile(0.95, http_request_duration_seconds_bucket) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应缓慢"
          description: "95%的请求响应时间超过500ms"
      
      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率"
          description: "5xx错误率超过5%"
      
      # 数据库连接告警
      - alert: DatabaseConnectionHigh
        expr: mysql_global_status_threads_connected > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "当前连接数: {{ $value }}"
```

### 3.4 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "PetCare监控面板",
    "panels": [
      {
        "title": "服务器资源使用",
        "targets": [
          {
            "expr": "100 - (avg(rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU使用率"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "内存使用率"
          }
        ]
      },
      {
        "title": "API性能指标",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "请求速率"
          },
          {
            "expr": "histogram_quantile(0.95, http_request_duration_seconds_bucket)",
            "legendFormat": "95%响应时间"
          }
        ]
      },
      {
        "title": "数据库性能",
        "targets": [
          {
            "expr": "mysql_global_status_queries",
            "legendFormat": "查询数"
          },
          {
            "expr": "mysql_global_status_slow_queries",
            "legendFormat": "慢查询数"
          }
        ]
      },
      {
        "title": "业务指标",
        "targets": [
          {
            "expr": "petcare_articles_total",
            "legendFormat": "文章总数"
          },
          {
            "expr": "rate(petcare_comments_created[1h])",
            "legendFormat": "评论速率"
          }
        ]
      }
    ]
  }
}
```

### 3.5 日志监控配置
```yaml
# filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /www/wwwlogs/*.log
      - /www/wwwroot/petcare/logs/*.log
    exclude_files: ['\.gz$']
    multiline.pattern: '^\['
    multiline.negate: true
    multiline.match: after
    
  - type: log
    enabled: true
    paths:
      - /var/log/nginx/access.log
    processors:
      - dissect:
          tokenizer: '%{clientip} - - [%{timestamp}] "%{method} %{uri} %{protocol}" %{status} %{size}'
          field: "message"
          target_prefix: "nginx.access"
    
output.elasticsearch:
  hosts: ["localhost:9200"]
  index: "petcare-%{+yyyy.MM.dd}"
  
processors:
  - add_docker_metadata: ~
  - add_host_metadata: ~
```

## 4. 故障处理

### 4.1 常见故障处理流程

#### 4.1.1 网站无法访问
```bash
#!/bin/bash
# troubleshoot-site-down.sh

echo "=== 网站故障排查 ==="

# 1. 检查网络连通性
echo "1. 检查网络连通性"
ping -c 3 www.petcare.com

# 2. 检查DNS解析
echo "2. 检查DNS解析"
nslookup www.petcare.com

# 3. 检查Nginx状态
echo "3. 检查Nginx状态"
systemctl status nginx
nginx -t

# 4. 检查Node.js进程
echo "4. 检查Node.js进程"
pm2 status

# 5. 检查端口监听
echo "5. 检查端口监听"
netstat -tlnp | grep -E "80|443|3000"

# 6. 检查日志
echo "6. 检查错误日志"
tail -50 /www/wwwlogs/petcare_en_error.log
tail -50 /www/wwwroot/petcare/logs/pm2-error.log

# 7. 检查磁盘空间
echo "7. 检查磁盘空间"
df -h

# 8. 检查系统资源
echo "8. 检查系统资源"
top -bn1 | head -20
```

#### 4.1.2 数据库连接失败
```bash
#!/bin/bash
# troubleshoot-db-connection.sh

echo "=== 数据库连接故障排查 ==="

# 1. 测试网络连接
echo "1. 测试数据库服务器连接"
ping -c 3 ************
telnet ************ 3306

# 2. 测试MySQL连接
echo "2. 测试MySQL连接"
mysql -h ************ -u bengtai -p${DB_PASSWORD} -e "SELECT 1"

# 3. 检查连接数
echo "3. 检查当前连接数"
mysql -h ************ -u bengtai -p${DB_PASSWORD} -e "SHOW PROCESSLIST"

# 4. 检查错误日志
echo "4. 检查应用错误日志"
grep -i "database\|mysql\|connection" /www/wwwroot/petcare/logs/pm2-error.log | tail -20

# 5. 重启应用
echo "5. 重启应用"
pm2 restart petcare-api
```

### 4.2 应急响应计划
```yaml
故障级别定义:
  P0 - 紧急:
    定义: 所有服务完全不可用
    响应时间: 15分钟内
    处理流程:
      1. 立即通知所有相关人员
      2. 启动应急响应小组
      3. 执行快速恢复流程
      4. 同步更新状态页面
  
  P1 - 高:
    定义: 核心功能受影响
    响应时间: 30分钟内
    处理流程:
      1. 通知值班人员
      2. 评估影响范围
      3. 执行修复流程
      4. 监控恢复情况
  
  P2 - 中:
    定义: 非核心功能异常
    响应时间: 2小时内
    处理流程:
      1. 记录问题详情
      2. 安排修复计划
      3. 通知相关团队
  
  P3 - 低:
    定义: 轻微问题
    响应时间: 24小时内
    处理流程:
      1. 记录到问题队列
      2. 排期处理
```

### 4.3 快速恢复流程
```bash
#!/bin/bash
# emergency-recovery.sh

echo "=== 执行紧急恢复流程 ==="

# 1. 切换到维护模式
echo "1. 启用维护模式"
cp /www/wwwroot/petcare/maintenance.html /www/wwwroot/petcare/frontend/en/index.html
cp /www/wwwroot/petcare/maintenance.html /www/wwwroot/petcare/frontend/de/index.html
cp /www/wwwroot/petcare/maintenance.html /www/wwwroot/petcare/frontend/ru/index.html

# 2. 重启所有服务
echo "2. 重启服务"
systemctl restart nginx
pm2 restart all
systemctl restart redis

# 3. 清理缓存
echo "3. 清理缓存"
redis-cli -a ${REDIS_PASSWORD} FLUSHDB

# 4. 检查服务状态
echo "4. 检查服务状态"
systemctl status nginx
pm2 status
redis-cli ping

# 5. 恢复正常模式
echo "5. 恢复服务"
cd /www/wwwroot/petcare/frontend/en && git checkout index.html
cd /www/wwwroot/petcare/frontend/de && git checkout index.html
cd /www/wwwroot/petcare/frontend/ru && git checkout index.html

# 6. 验证恢复
echo "6. 验证恢复"
curl -I https://www.petcare.com
curl -I https://www.haustiere.de
curl -I https://www.домашние-животные.рф

echo "=== 恢复流程完成 ==="
```

## 5. 性能优化

### 5.1 定期性能分析
```bash
#!/bin/bash
# performance-analysis.sh

echo "=== 月度性能分析 ==="

# 1. 分析慢查询
echo "1. MySQL慢查询分析"
pt-query-digest /var/log/mysql/slow-query.log > /www/wwwlogs/slow-query-analysis-$(date +%Y%m).txt

# 2. 分析Nginx访问日志
echo "2. Nginx访问分析"
goaccess /www/wwwlogs/petcare_en_access.log -o /www/wwwlogs/nginx-report-$(date +%Y%m).html

# 3. 分析API性能
echo "3. API性能分析"
cat /www/wwwroot/petcare/logs/api-metrics.log | \
  awk '{sum+=$2; count++} END {print "平均响应时间:", sum/count, "ms"}'

# 4. Redis性能分析
echo "4. Redis性能分析"
redis-cli -a ${REDIS_PASSWORD} INFO stats
redis-cli -a ${REDIS_PASSWORD} --latency

# 5. 生成优化建议
echo "5. 生成优化建议"
/www/server/panel/generate-optimization-report.sh
```

### 5.2 数据库优化脚本
```sql
-- optimize-database.sql

-- 1. 更新统计信息
ANALYZE TABLE articles;
ANALYZE TABLE article_translations;
ANALYZE TABLE comments;
ANALYZE TABLE categories;

-- 2. 优化表
OPTIMIZE TABLE articles;
OPTIMIZE TABLE article_translations;
OPTIMIZE TABLE comments;

-- 3. 检查并修复索引
-- 查找未使用的索引
SELECT 
    s.table_schema,
    s.table_name,
    s.index_name,
    s.cardinality
FROM information_schema.statistics s
LEFT JOIN sys.schema_unused_indexes u 
    ON s.table_schema = u.object_schema 
    AND s.table_name = u.object_name 
    AND s.index_name = u.index_name
WHERE s.table_schema = 'bengtai'
    AND u.index_name IS NOT NULL;

-- 4. 检查缺失的索引
SELECT 
    tables.table_schema,
    tables.table_name,
    tables.table_rows,
    tables.data_length,
    tables.index_length
FROM information_schema.tables
WHERE table_schema = 'bengtai'
    AND table_type = 'BASE TABLE'
ORDER BY data_length DESC;

-- 5. 清理碎片
ALTER TABLE articles ENGINE=InnoDB;
ALTER TABLE article_translations ENGINE=InnoDB;
```

### 5.3 缓存优化策略
```javascript
// cache-optimization.js
const redis = require('redis');
const client = redis.createClient({
  host: 'localhost',
  port: 6379,
  password: process.env.REDIS_PASSWORD,
});

async function analyzeCacheEfficiency() {
  // 获取缓存统计
  const info = await client.info('stats');
  
  // 解析命中率
  const hitRate = calculateHitRate(info);
  console.log(`缓存命中率: ${hitRate}%`);
  
  // 分析键空间
  const keys = await client.keys('*');
  const keyPatterns = analyzeKeyPatterns(keys);
  
  // 生成优化建议
  const recommendations = [];
  
  if (hitRate < 80) {
    recommendations.push('缓存命中率低，建议：');
    recommendations.push('- 增加热门数据的缓存时间');
    recommendations.push('- 预加载常用数据');
    recommendations.push('- 优化缓存键设计');
  }
  
  // 检查大键
  const bigKeys = await findBigKeys();
  if (bigKeys.length > 0) {
    recommendations.push('发现大键，建议拆分或压缩：');
    bigKeys.forEach(key => {
      recommendations.push(`- ${key.name}: ${key.size}`);
    });
  }
  
  return recommendations;
}

// 执行优化
analyzeCacheEfficiency()
  .then(recommendations => {
    console.log('缓存优化建议：');
    recommendations.forEach(r => console.log(r));
  })
  .catch(console.error);
```

## 6. 安全维护

### 6.1 安全检查脚本
```bash
#!/bin/bash
# security-check.sh

echo "=== 月度安全检查 ==="

# 1. 检查系统更新
echo "1. 检查安全更新"
apt update
apt list --upgradable | grep -i security

# 2. 检查开放端口
echo "2. 检查开放端口"
netstat -tlnp | grep LISTEN

# 3. 检查异常登录
echo "3. 检查异常登录"
lastb | head -20
grep "Failed password" /var/log/auth.log | tail -20

# 4. 检查文件完整性
echo "4. 检查关键文件修改"
find /www/wwwroot/petcare -type f -name "*.php" -o -name "*.js" -mtime -7 | head -20

# 5. 扫描恶意软件
echo "5. 扫描恶意软件"
clamscan -r /www/wwwroot/petcare --exclude-dir=node_modules

# 6. 检查SSL证书
echo "6. 检查SSL证书有效期"
for domain in www.petcare.com www.haustiere.de; do
    echo "检查 $domain"
    echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | \
      openssl x509 -noout -enddate
done

# 7. 审计日志分析
echo "7. 审计日志分析"
aureport --summary
```

### 6.2 安全加固脚本
```bash
#!/bin/bash
# security-hardening.sh

echo "=== 执行安全加固 ==="

# 1. 更新防火墙规则
echo "1. 更新防火墙规则"
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow from 10.0.0.0/8 to any port 3306
ufw allow from 10.0.0.0/8 to any port 6379
ufw --force enable

# 2. 配置fail2ban
echo "2. 配置fail2ban"
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = 22
filter = sshd
logpath = /var/log/auth.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /www/wwwlogs/*error.log
EOF

systemctl restart fail2ban

# 3. 设置文件权限
echo "3. 设置文件权限"
find /www/wwwroot/petcare -type d -exec chmod 755 {} \;
find /www/wwwroot/petcare -type f -exec chmod 644 {} \;
chmod 600 /www/wwwroot/petcare/backend/.env

# 4. 禁用不必要的服务
echo "4. 禁用不必要的服务"
systemctl disable bluetooth
systemctl disable cups

# 5. 配置内核参数
echo "5. 优化内核安全参数"
cat >> /etc/sysctl.conf << EOF
# IP Spoofing protection
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1

# Ignore ICMP redirects
net.ipv4.conf.all.accept_redirects = 0
net.ipv6.conf.all.accept_redirects = 0

# Ignore send redirects
net.ipv4.conf.all.send_redirects = 0

# Disable source packet routing
net.ipv4.conf.all.accept_source_route = 0
net.ipv6.conf.all.accept_source_route = 0

# Log Martians
net.ipv4.conf.all.log_martians = 1

# Ignore ICMP ping requests
net.ipv4.icmp_echo_ignore_broadcasts = 1

# SYN flood protection
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 2048
net.ipv4.tcp_synack_retries = 2
net.ipv4.tcp_syn_retries = 5
EOF

sysctl -p

echo "=== 安全加固完成 ==="
```

## 7. 数据库维护

### 7.1 数据库健康检查
```sql
-- db-health-check.sql

-- 1. 检查表状态
SELECT 
    table_name,
    table_rows,
    avg_row_length,
    data_length/1024/1024 as data_size_mb,
    index_length/1024/1024 as index_size_mb,
    data_free/1024/1024 as free_size_mb
FROM information_schema.tables
WHERE table_schema = 'bengtai'
ORDER BY data_length DESC;

-- 2. 检查索引使用情况
SELECT 
    object_name,
    index_name,
    count_star,
    count_read,
    count_write
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE object_schema = 'bengtai'
    AND index_name IS NOT NULL
ORDER BY count_star DESC;

-- 3. 检查慢查询
SELECT 
    query,
    count,
    total_time,
    mean_time,
    max_time
FROM sys.statement_analysis
WHERE db = 'bengtai'
ORDER BY total_time DESC
LIMIT 10;

-- 4. 检查锁等待
SELECT 
    waiting_trx_id,
    waiting_pid,
    waiting_query,
    blocking_trx_id,
    blocking_pid,
    blocking_query
FROM sys.innodb_lock_waits;

-- 5. 检查连接状态
SELECT 
    user,
    host,
    db,
    command,
    time,
    state
FROM information_schema.processlist
WHERE db = 'bengtai'
ORDER BY time DESC;
```

### 7.2 数据归档策略
```bash
#!/bin/bash
# archive-old-data.sh

# 配置
ARCHIVE_DB="bengtai_archive"
DAYS_TO_KEEP=365

echo "=== 开始数据归档 ==="

# 1. 创建归档表
mysql -h ************ -u bengtai -p${DB_PASSWORD} << EOF
CREATE DATABASE IF NOT EXISTS ${ARCHIVE_DB};
USE ${ARCHIVE_DB};

-- 创建归档表结构（如果不存在）
CREATE TABLE IF NOT EXISTS articles_archive LIKE bengtai.articles;
CREATE TABLE IF NOT EXISTS article_translations_archive LIKE bengtai.article_translations;
CREATE TABLE IF NOT EXISTS comments_archive LIKE bengtai.comments;
EOF

# 2. 归档旧数据
mysql -h ************ -u bengtai -p${DB_PASSWORD} << EOF
-- 归档文章
INSERT INTO ${ARCHIVE_DB}.articles_archive
SELECT * FROM bengtai.articles
WHERE created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
    AND status = 'archived';

-- 归档翻译
INSERT INTO ${ARCHIVE_DB}.article_translations_archive
SELECT at.* FROM bengtai.article_translations at
INNER JOIN bengtai.articles a ON at.article_id = a.id
WHERE a.created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
    AND a.status = 'archived';

-- 归档评论
INSERT INTO ${ARCHIVE_DB}.comments_archive
SELECT c.* FROM bengtai.comments c
INNER JOIN bengtai.articles a ON c.article_id = a.id
WHERE a.created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
    AND a.status = 'archived';

-- 删除已归档的数据
DELETE FROM bengtai.comments
WHERE article_id IN (
    SELECT id FROM bengtai.articles
    WHERE created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
        AND status = 'archived'
);

DELETE FROM bengtai.article_translations
WHERE article_id IN (
    SELECT id FROM bengtai.articles
    WHERE created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
        AND status = 'archived'
);

DELETE FROM bengtai.articles
WHERE created_at < DATE_SUB(NOW(), INTERVAL ${DAYS_TO_KEEP} DAY)
    AND status = 'archived';
EOF

echo "=== 数据归档完成 ==="
```

## 8. 灾难恢复

### 8.1 灾难恢复计划
```yaml
恢复目标:
  RTO (恢复时间目标): 4小时
  RPO (恢复点目标): 24小时
  
恢复优先级:
  1. 数据库恢复
  2. 应用服务恢复
  3. 静态资源恢复
  4. 历史数据恢复
  
恢复场景:
  服务器故障:
    - 启用备用服务器
    - 恢复最新备份
    - 切换DNS
    
  数据损坏:
    - 停止写入操作
    - 评估损坏范围
    - 从备份恢复
    
  安全事件:
    - 隔离受影响系统
    - 评估入侵范围
    - 清理并恢复
    
  自然灾害:
    - 激活异地备份
    - 通知用户
    - 逐步恢复服务
```

### 8.2 恢复脚本
```bash
#!/bin/bash
# disaster-recovery.sh

echo "=== 灾难恢复流程 ==="

# 参数
RECOVERY_TYPE=$1  # full, database, files
BACKUP_DATE=$2    # YYYYMMDD

if [ -z "$RECOVERY_TYPE" ] || [ -z "$BACKUP_DATE" ]; then
    echo "用法: $0 <recovery_type> <backup_date>"
    echo "recovery_type: full, database, files"
    echo "backup_date: YYYYMMDD"
    exit 1
fi

# 1. 确认恢复
echo "警告：此操作将覆盖现有数据！"
read -p "确定要执行恢复吗？(yes/no): " confirm
if [ "$confirm" != "yes" ]; then
    echo "恢复已取消"
    exit 0
fi

# 2. 停止服务
echo "2. 停止服务"
systemctl stop nginx
pm2 stop all

# 3. 执行恢复
case $RECOVERY_TYPE in
    "full")
        echo "3. 执行完整恢复"
        # 恢复数据库
        gunzip < /backup/mysql/db_full_${BACKUP_DATE}*.sql.gz | \
          mysql -h ************ -u bengtai -p${DB_PASSWORD} bengtai
        
        # 恢复文件
        tar -xzf /backup/files/files_${BACKUP_DATE}*.tar.gz -C /
        ;;
        
    "database")
        echo "3. 执行数据库恢复"
        gunzip < /backup/mysql/db_full_${BACKUP_DATE}*.sql.gz | \
          mysql -h ************ -u bengtai -p${DB_PASSWORD} bengtai
        ;;
        
    "files")
        echo "3. 执行文件恢复"
        tar -xzf /backup/files/files_${BACKUP_DATE}*.tar.gz -C /
        ;;
esac

# 4. 清理缓存
echo "4. 清理缓存"
redis-cli -a ${REDIS_PASSWORD} FLUSHDB

# 5. 启动服务
echo "5. 启动服务"
systemctl start nginx
pm2 start all

# 6. 验证恢复
echo "6. 验证恢复"
sleep 5
curl -I https://www.petcare.com
pm2 status

echo "=== 恢复完成 ==="
echo "请立即检查系统功能是否正常！"
```

### 8.3 恢复验证清单
```yaml
恢复后验证:
  基础功能:
    - [ ] 首页可访问
    - [ ] 文章列表正常
    - [ ] 文章详情可查看
    - [ ] 图片正常显示
    - [ ] 搜索功能正常
    
  数据完整性:
    - [ ] 文章数量正确
    - [ ] 用户数据完整
    - [ ] 评论数据正常
    - [ ] 媒体文件可访问
    
  系统功能:
    - [ ] API响应正常
    - [ ] 数据库连接正常
    - [ ] 缓存工作正常
    - [ ] 日志记录正常
    
  安全检查:
    - [ ] SSL证书有效
    - [ ] 登录功能正常
    - [ ] 权限控制正常
    - [ ] 无异常访问
```

## 维护日志模板

```markdown
# 维护日志 - 2025年1月

## 2025-01-20
### 执行的维护任务
- [x] 每日健康检查
- [x] 清理日志文件（释放2.3GB空间）
- [x] 数据库备份验证

### 发现的问题
- API响应时间偶尔超过300ms
- 磁盘使用率达到78%

### 采取的措施
- 优化了数据库查询索引
- 计划下周进行磁盘扩容

### 系统指标
- CPU平均使用率: 45%
- 内存使用率: 72%
- 磁盘使用率: 78%
- API平均响应时间: 186ms
```

## 总结

本维护文档提供了全面的运维指南，涵盖日常维护、备份恢复、监控告警、故障处理等各个方面。通过规范化的维护流程和自动化脚本，可以确保系统稳定、安全、高效地运行。

### 关键要点
1. **预防为主**：通过定期检查和维护预防故障
2. **自动化运维**：使用脚本自动化日常任务
3. **完善监控**：多层次监控及时发现问题
4. **快速恢复**：制定详细的应急和恢复方案
5. **持续优化**：定期分析和优化系统性能

### 维护建议
1. 严格执行日常检查清单
2. 定期演练灾难恢复流程
3. 保持文档和脚本更新
4. 建立知识库记录问题和解决方案
5. 定期培训提升运维技能