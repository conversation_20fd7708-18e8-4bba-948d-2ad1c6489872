# 宠物博客站群系统 - 项目架构文档

## 目录
- [1. 项目概述](#1-项目概述)
- [2. 技术栈选型](#2-技术栈选型)
- [3. 系统架构设计](#3-系统架构设计)
- [4. 数据流程设计](#4-数据流程设计)
- [5. 部署架构](#5-部署架构)
- [6. 安全架构](#6-安全架构)
- [7. 性能架构](#7-性能架构)
- [8. 扩展性设计](#8-扩展性设计)

## 1. 项目概述

### 1.1 项目定位
宠物博客多语言站群系统是一个面向全球宠物爱好者的内容平台，通过AI辅助翻译和本地化优化，为不同国家和地区的用户提供高质量的宠物养护知识。

### 1.2 核心特性
- **多语言独立架构**：每个语言版本作为独立站点运行
- **AI翻译工作流**：集成Gemini AI实现高效内容翻译
- **SEO优先设计**：严格遵循Google 2025最新SEO标准
- **高性能架构**：静态生成 + 边缘缓存实现极速访问
- **模块化扩展**：支持灵活添加新语言和功能模块

### 1.3 目标用户
- **内容运营者**：通过后台管理系统发布和管理内容
- **终端用户**：在各语言站点浏览宠物相关内容
- **系统管理员**：维护和监控整个站群系统

## 2. 技术栈选型

### 2.1 前端技术栈
```yaml
框架: Astro 4.0+
  选型理由:
    - Islands架构实现按需加载
    - 优秀的SSG性能
    - 内置SEO优化支持
    - 多框架组件兼容

样式方案: Tailwind CSS 3.4+
  选型理由:
    - 原子化CSS减少样式体积
    - 响应式设计便捷实现
    - 主题定制灵活
    - 构建时自动清理未使用样式

构建工具: Vite
  选型理由:
    - 快速的开发服务器
    - 高效的生产构建
    - 丰富的插件生态
```

### 2.2 后端技术栈
```yaml
运行时: Node.js 20 LTS
框架: Express 4.19+
  选型理由:
    - 成熟稳定的生态系统
    - 中间件机制灵活
    - 宝塔面板友好支持
    - 社区资源丰富

API风格: RESTful
  选型理由:
    - 简单直观的设计
    - 缓存友好
    - 工具链完善
    - 易于测试和文档化

认证方案: JWT
  选型理由:
    - 无状态特性
    - 分布式友好
    - 客户端存储
    - 跨域支持良好
```

### 2.3 数据存储
```yaml
主数据库: MySQL 9.0.1
  连接配置:
    host: ************
    database: bengtai
    user: bengtai
    password: weizhen258
    charset: utf8mb4
    collation: utf8mb4_unicode_ci

缓存层: Redis 7.2+
  用途:
    - 热门文章缓存
    - 会话存储
    - 翻译结果缓存
    - API限流计数

文件存储: 本地磁盘 + CDN
  策略:
    - 原始图片本地存储
    - CDN加速分发
    - WebP自动转换
    - 分级存储管理
```

### 2.4 第三方服务
```yaml
AI翻译: Gemini API
  配置:
    endpoint: https://ai.wanderintree.top
    apiKey: sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
    model: gemini-2.5-pro
    
CDN服务: Cloudflare (推荐)
  功能:
    - 全球加速
    - DDoS防护
    - 图片优化
    - 边缘缓存

监控服务:
  - Google Analytics 4 (流量分析)
  - Sentry (错误监控)
  - Uptime Robot (可用性监控)
```

## 3. 系统架构设计

### 3.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        CDN Layer                             │
│  (Static Assets, Images, Cached Pages)                      │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────┴───────────────────────────────────────────┐
│                    Frontend Layer                            │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐                  │
│  │ EN Site  │  │ DE Site  │  │ RU Site  │  ...             │
│  │ (Astro)  │  │ (Astro)  │  │ (Astro)  │                  │
│  └──────────┘  └──────────┘  └──────────┘                  │
└─────────────────┬───────────────────────────────────────────┘
                  │ API Calls
┌─────────────────┴───────────────────────────────────────────┐
│                     API Gateway                              │
│           (Rate Limiting, Authentication)                    │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────┴───────────────────────────────────────────┐
│                   Backend Services                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   CMS API   │  │Translation  │  │  Admin API  │        │
│  │  (Express)  │  │   Service   │  │  (Express)  │        │
│  └──────┬──────┘  └──────┬──────┘  └──────┬──────┘        │
│         │                 │                 │                │
│  ┌──────┴─────────────────┴─────────────────┴──────┐        │
│  │              Service Layer                       │        │
│  │  (Business Logic, Validation, Caching)         │        │
│  └──────────────────────┬──────────────────────────┘        │
└─────────────────────────┴───────────────────────────────────┘
                          │
┌─────────────────────────┴───────────────────────────────────┐
│                    Data Layer                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    MySQL    │  │    Redis    │  │File Storage │        │
│  │  (Remote)   │  │   (Cache)   │  │   (Local)   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 模块划分

#### 3.2.1 前端模块
```yaml
公共组件:
  - Layout (页面布局框架)
  - Navigation (导航组件)
  - ArticleCard (文章卡片)
  - Comment (评论组件)
  - SEO (SEO元数据组件)

页面模块:
  - HomePage (首页)
  - CategoryPage (分类页)
  - ArticlePage (文章详情)
  - SearchPage (搜索结果)
  - ErrorPage (错误页面)

工具模块:
  - API Client (API请求封装)
  - Image Optimizer (图片优化)
  - Analytics (统计集成)
  - Ad Manager (广告管理)
```

#### 3.2.2 后端模块
```yaml
核心模块:
  - Authentication (认证中间件)
  - Authorization (授权管理)
  - Validation (数据验证)
  - Error Handler (错误处理)

业务模块:
  - Article Service (文章管理)
  - Translation Service (翻译服务)
  - Comment Service (评论管理)
  - Site Config Service (站点配置)

工具模块:
  - Database (数据库连接池)
  - Cache Manager (缓存管理)
  - Queue Manager (任务队列)
  - Logger (日志记录)
```

### 3.3 微服务划分考虑
虽然初期采用单体架构，但设计时预留微服务拆分可能：

```yaml
潜在的服务拆分:
  1. Content Service (内容管理核心)
  2. Translation Service (翻译服务)
  3. Comment Service (评论系统)
  4. Analytics Service (数据分析)
  5. Media Service (媒体处理)
```

## 4. 数据流程设计

### 4.1 内容发布流程
```mermaid
graph TD
    A[编辑撰写中文原稿] --> B[保存到文章表]
    B --> C{选择目标语言}
    C --> D[调用AI翻译API]
    D --> E[保存翻译草稿]
    E --> F[人工审核界面]
    F --> G{审核通过?}
    G -->|是| H[更新翻译状态]
    G -->|否| I[修改翻译内容]
    I --> F
    H --> J[生成静态页面]
    J --> K[部署到CDN]
    K --> L[更新站点地图]
    L --> M[通知搜索引擎]
```

### 4.2 用户访问流程
```mermaid
graph TD
    A[用户访问] --> B[CDN边缘节点]
    B --> C{缓存命中?}
    C -->|是| D[返回缓存页面]
    C -->|否| E[请求源站]
    E --> F[Astro服务器]
    F --> G{需要API数据?}
    G -->|是| H[调用后端API]
    G -->|否| I[返回静态页面]
    H --> J[查询缓存]
    J --> K{缓存存在?}
    K -->|是| L[返回缓存数据]
    K -->|否| M[查询数据库]
    M --> N[更新缓存]
    N --> L
    L --> O[渲染页面]
    O --> P[返回给用户]
    I --> P
    D --> P
```

### 4.3 评论提交流程
```mermaid
graph TD
    A[用户提交评论] --> B[前端验证]
    B --> C{验证通过?}
    C -->|否| D[显示错误提示]
    C -->|是| E[提交到API]
    E --> F[后端验证]
    F --> G[垃圾评论检测]
    G --> H{检测通过?}
    H -->|否| I[拒绝评论]
    H -->|是| J[保存到待审核]
    J --> K[管理员审核]
    K --> L{审核通过?}
    L -->|否| M[删除评论]
    L -->|是| N[发布评论]
    N --> O[更新缓存]
    O --> P[通知用户]
```

## 5. 部署架构

### 5.1 服务器配置
```yaml
生产环境:
  Web服务器:
    - OS: Ubuntu 22.04 LTS
    - 配置: 4核8G内存
    - 软件: 宝塔面板 + Nginx
    - Node.js: PM2进程管理
    
  数据库服务器:
    - 类型: 远程MySQL
    - 地址: ************
    - 配置: 已提供
    
  缓存服务器:
    - Redis: 本地部署
    - 内存: 2GB专用
```

### 5.2 域名配置策略
```yaml
域名结构:
  英文站: www.petcare.com
  德文站: www.haustiere.de
  俄文站: www.домашние-животные.рф
  
DNS配置:
  - A记录指向服务器IP
  - CNAME记录指向CDN
  - TXT记录验证所有权
  
SSL证书:
  - Let's Encrypt免费证书
  - 自动续期配置
  - HSTS安全头
```

### 5.3 持续部署流程
```yaml
CI/CD Pipeline:
  1. 代码提交到Git仓库
  2. 触发GitHub Actions
  3. 运行测试套件
  4. 构建生产版本
  5. 部署到服务器
  6. 清理CDN缓存
  7. 健康检查
  8. 通知部署结果
```

## 6. 安全架构

### 6.1 应用安全
```yaml
认证授权:
  - JWT Token有效期: 7天
  - Refresh Token: 30天
  - 权限基于角色(RBAC)
  - 会话管理严格

数据安全:
  - SQL预编译防注入
  - XSS自动转义
  - CSRF Token验证
  - 敏感数据加密存储

API安全:
  - Rate Limiting (100/分钟)
  - API Key验证
  - IP白名单(可选)
  - 请求签名验证
```

### 6.2 基础设施安全
```yaml
服务器安全:
  - 防火墙规则配置
  - SSH密钥认证
  - 定期安全更新
  - 入侵检测系统

网络安全:
  - DDoS防护(CDN层)
  - WAF规则配置
  - SSL/TLS加密
  - 安全响应头
```

## 7. 性能架构

### 7.1 性能指标
```yaml
Core Web Vitals:
  - LCP (最大内容绘制): < 2.5秒
  - FID (首次输入延迟): < 100毫秒
  - CLS (累积布局偏移): < 0.1

其他指标:
  - 首屏加载时间: < 3秒
  - API响应时间: < 200毫秒
  - 并发支持: 1000+/秒
  - 可用性: 99.9%
```

### 7.2 优化策略
```yaml
前端优化:
  - 代码分割和懒加载
  - 图片优化(WebP + 懒加载)
  - 资源预加载和预连接
  - Service Worker缓存

后端优化:
  - 数据库查询优化
  - Redis缓存策略
  - 连接池管理
  - 异步任务队列

CDN优化:
  - 边缘缓存规则
  - 压缩算法(Brotli)
  - HTTP/2推送
  - 智能路由
```

### 7.3 缓存策略
```yaml
多级缓存:
  1. 浏览器缓存
     - 静态资源: 1年
     - API响应: 根据内容
     
  2. CDN缓存
     - HTML页面: 1小时
     - 静态资源: 1个月
     - API响应: 5分钟
     
  3. Redis缓存
     - 热门文章: 1天
     - 分类数据: 6小时
     - 用户会话: 7天
     
  4. 应用缓存
     - 内存缓存: LRU策略
     - 计算结果: 按需
```

## 8. 扩展性设计

### 8.1 语言扩展
```yaml
新增语言步骤:
  1. 复制现有语言模板
  2. 配置语言参数
  3. 设置域名映射
  4. 初始化内容分类
  5. 配置SEO规则
  6. 部署新站点
  
自动化支持:
  - 模板生成脚本
  - 配置向导
  - 一键部署
```

### 8.2 功能扩展
```yaml
插件化设计:
  - 广告模块(可插拔)
  - 统计模块(可替换)
  - 评论系统(可扩展)
  - 搜索引擎(可升级)
  
扩展接口:
  - Webhook支持
  - 事件总线
  - 插件API
  - 主题系统
```

### 8.3 容量规划
```yaml
扩展阈值:
  - 日PV > 100万: 考虑负载均衡
  - 内容 > 10万: 考虑分库分表
  - 语言 > 10个: 考虑微服务
  - 并发 > 5000: 考虑集群

扩展方案:
  - 水平扩展: 增加服务器
  - 垂直扩展: 升级配置
  - 服务拆分: 微服务化
  - 数据分片: 分库分表
```

## 总结

本架构设计充分考虑了项目的当前需求和未来扩展，采用成熟稳定的技术栈，确保系统的高性能、高可用和易维护。通过模块化设计和清晰的层次结构，为后续的开发和维护提供了良好的基础。

### 关键决策点
1. **Astro + SSG**: 最大化SEO效果和页面性能
2. **独立语言站点**: 避免i18n性能损耗，提升本地化体验
3. **Express + MySQL**: 成熟稳定，部署运维简单
4. **多级缓存**: 确保高并发下的稳定性能
5. **模块化设计**: 便于功能扩展和团队协作

### 下一步行动
1. 搭建开发环境
2. 初始化项目结构
3. 实现数据库设计
4. 开发核心API
5. 创建前端模板