# 宠物博客站群系统 - 数据库设计文档

## 目录
- [1. 数据库概述](#1-数据库概述)
- [2. ER图设计](#2-er图设计)
- [3. 表结构详细设计](#3-表结构详细设计)
- [4. 索引设计策略](#4-索引设计策略)
- [5. 数据完整性设计](#5-数据完整性设计)
- [6. 性能优化策略](#6-性能优化策略)
- [7. 数据迁移方案](#7-数据迁移方案)
- [8. SQL脚本示例](#8-sql脚本示例)

## 1. 数据库概述

### 1.1 基本信息
```yaml
数据库类型: MySQL 9.0.1
字符集: utf8mb4
排序规则: utf8mb4_unicode_ci
存储引擎: InnoDB
连接信息:
  host: ************
  database: bengtai
  username: bengtai
  password: weizhen258
```

### 1.2 设计原则
1. **规范化设计**：遵循第三范式，避免数据冗余
2. **多语言支持**：通过关联表实现内容的多语言管理
3. **性能优先**：合理使用索引和分区策略
4. **扩展性考虑**：预留字段和表结构便于后续扩展
5. **数据完整性**：使用外键约束和触发器保证数据一致性

### 1.3 命名规范
- 表名：小写字母，单词间用下划线分隔
- 字段名：小写字母，单词间用下划线分隔
- 索引名：idx_表名_字段名
- 外键名：fk_子表_父表_字段名

## 2. ER图设计

### 2.1 核心实体关系图
```
┌─────────────┐         ┌──────────────────┐         ┌─────────────┐
│   users     │         │     articles     │         │ categories  │
├─────────────┤         ├──────────────────┤         ├─────────────┤
│ id          │    1:N  │ id               │    N:1  │ id          │
│ username    ├─────────┤ author_id        ├─────────┤ parent_id   │
│ email       │         │ category_id      │         │ sort_order  │
│ password    │         │ status           │         │ created_at  │
│ role        │         │ view_count       │         └─────────────┘
│ created_at  │         │ created_at       │
└─────────────┘         │ updated_at       │
                        └────────┬─────────┘
                                 │ 1:N
                        ┌────────┴──────────┐
                        │ article_translations │
                        ├───────────────────┤
                        │ id                │
                        │ article_id        │
                        │ language_code     │
                        │ title             │
                        │ slug              │
                        │ content           │
                        │ summary           │
                        │ meta_title        │
                        │ meta_description  │
                        │ meta_keywords     │
                        │ translation_status│
                        │ translated_at     │
                        │ published_at      │
                        └───────────────────┘
```

### 2.2 评论系统关系图
```
┌──────────────────┐         ┌─────────────────┐
│    articles      │         │    comments     │
├──────────────────┤    1:N  ├─────────────────┤
│ id               ├─────────┤ id              │
│ ...              │         │ article_id      │
└──────────────────┘         │ parent_id       │
                             │ language_code   │
                             │ author_name     │
                             │ author_email    │
                             │ content         │
                             │ status          │
                             │ ip_address      │
                             │ user_agent      │
                             │ created_at      │
                             └─────────────────┘
```

### 2.3 站点配置关系图
```
┌─────────────────┐         ┌──────────────────┐         ┌─────────────────┐
│     sites       │         │   site_configs   │         │   ad_configs    │
├─────────────────┤    1:N  ├──────────────────┤    1:N  ├─────────────────┤
│ id              ├─────────┤ id               ├─────────┤ id              │
│ domain          │         │ site_id          │         │ site_id         │
│ language_code   │         │ config_key       │         │ position        │
│ title           │         │ config_value     │         │ ad_code         │
│ description     │         │ created_at       │         │ is_active       │
│ is_active       │         └──────────────────┘         │ created_at      │
│ created_at      │                                      └─────────────────┘
└─────────────────┘
```

## 3. 表结构详细设计

### 3.1 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL,
  `email` VARCHAR(100) NOT NULL,
  `password` VARCHAR(255) NOT NULL COMMENT 'bcrypt加密',
  `role` ENUM('super_admin', 'admin', 'editor') NOT NULL DEFAULT 'editor',
  `avatar` VARCHAR(255) DEFAULT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `last_login_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_username` (`username`),
  UNIQUE KEY `idx_users_email` (`email`),
  KEY `idx_users_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.2 分类表 (categories)
```sql
CREATE TABLE `categories` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` INT UNSIGNED DEFAULT NULL,
  `slug` VARCHAR(100) NOT NULL,
  `icon` VARCHAR(100) DEFAULT NULL,
  `sort_order` INT NOT NULL DEFAULT 0,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_categories_slug` (`slug`),
  KEY `idx_categories_parent` (`parent_id`),
  KEY `idx_categories_sort` (`sort_order`),
  CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`) 
    REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.3 分类翻译表 (category_translations)
```sql
CREATE TABLE `category_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` INT UNSIGNED NOT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `meta_title` VARCHAR(200),
  `meta_description` VARCHAR(300),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_trans_unique` (`category_id`, `language_code`),
  KEY `idx_category_trans_lang` (`language_code`),
  CONSTRAINT `fk_category_trans_category` FOREIGN KEY (`category_id`) 
    REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.4 文章表 (articles)
```sql
CREATE TABLE `articles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `author_id` INT UNSIGNED NOT NULL,
  `category_id` INT UNSIGNED NOT NULL,
  `featured_image` VARCHAR(255) DEFAULT NULL,
  `status` ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
  `view_count` INT UNSIGNED NOT NULL DEFAULT 0,
  `comment_count` INT UNSIGNED NOT NULL DEFAULT 0,
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE,
  `published_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_articles_author` (`author_id`),
  KEY `idx_articles_category` (`category_id`),
  KEY `idx_articles_status` (`status`),
  KEY `idx_articles_published` (`published_at`),
  KEY `idx_articles_featured` (`is_featured`, `published_at`),
  CONSTRAINT `fk_articles_author` FOREIGN KEY (`author_id`) 
    REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_articles_category` FOREIGN KEY (`category_id`) 
    REFERENCES `categories` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.5 文章翻译表 (article_translations)
```sql
CREATE TABLE `article_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL,
  `language_code` VARCHAR(5) NOT NULL DEFAULT 'zh-CN',
  `title` VARCHAR(200) NOT NULL,
  `slug` VARCHAR(200) NOT NULL,
  `content` LONGTEXT NOT NULL,
  `summary` TEXT,
  `meta_title` VARCHAR(200),
  `meta_description` VARCHAR(300),
  `meta_keywords` VARCHAR(200),
  `translation_status` ENUM('original', 'ai_translated', 'human_reviewed', 'published') 
    NOT NULL DEFAULT 'original',
  `ai_translation_id` VARCHAR(100) DEFAULT NULL COMMENT 'AI翻译任务ID',
  `translator_id` INT UNSIGNED DEFAULT NULL COMMENT '人工审核者ID',
  `translated_at` TIMESTAMP NULL DEFAULT NULL,
  `reviewed_at` TIMESTAMP NULL DEFAULT NULL,
  `published_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_article_trans_unique` (`article_id`, `language_code`),
  UNIQUE KEY `idx_article_trans_slug` (`language_code`, `slug`),
  KEY `idx_article_trans_status` (`translation_status`),
  KEY `idx_article_trans_published` (`language_code`, `published_at`),
  FULLTEXT KEY `idx_article_trans_fulltext` (`title`, `content`),
  CONSTRAINT `fk_article_trans_article` FOREIGN KEY (`article_id`) 
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_trans_translator` FOREIGN KEY (`translator_id`) 
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.6 文章标签表 (tags)
```sql
CREATE TABLE `tags` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `slug` VARCHAR(50) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tags_slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.7 文章标签关联表 (article_tags)
```sql
CREATE TABLE `article_tags` (
  `article_id` INT UNSIGNED NOT NULL,
  `tag_id` INT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_article_tags_tag` (`tag_id`),
  CONSTRAINT `fk_article_tags_article` FOREIGN KEY (`article_id`) 
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tags_tag` FOREIGN KEY (`tag_id`) 
    REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.8 评论表 (comments)
```sql
CREATE TABLE `comments` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL,
  `parent_id` INT UNSIGNED DEFAULT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `author_name` VARCHAR(50) NOT NULL,
  `author_email` VARCHAR(100) NOT NULL,
  `author_url` VARCHAR(200) DEFAULT NULL,
  `content` TEXT NOT NULL,
  `status` ENUM('pending', 'approved', 'spam', 'trash') NOT NULL DEFAULT 'pending',
  `ip_address` VARCHAR(45) NOT NULL,
  `user_agent` VARCHAR(255),
  `approved_by` INT UNSIGNED DEFAULT NULL,
  `approved_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_comments_article` (`article_id`, `status`),
  KEY `idx_comments_parent` (`parent_id`),
  KEY `idx_comments_status` (`status`, `created_at`),
  KEY `idx_comments_email` (`author_email`),
  CONSTRAINT `fk_comments_article` FOREIGN KEY (`article_id`) 
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) 
    REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_approver` FOREIGN KEY (`approved_by`) 
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.9 站点配置表 (sites)
```sql
CREATE TABLE `sites` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `domain` VARCHAR(100) NOT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `title` VARCHAR(200) NOT NULL,
  `description` TEXT,
  `logo` VARCHAR(255) DEFAULT NULL,
  `favicon` VARCHAR(255) DEFAULT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `robots_txt` TEXT,
  `sitemap_url` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_sites_domain` (`domain`),
  KEY `idx_sites_language` (`language_code`),
  KEY `idx_sites_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.10 站点配置项表 (site_configs)
```sql
CREATE TABLE `site_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `site_id` INT UNSIGNED NOT NULL,
  `config_key` VARCHAR(100) NOT NULL,
  `config_value` TEXT,
  `config_type` ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
  `description` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_site_configs_unique` (`site_id`, `config_key`),
  KEY `idx_site_configs_key` (`config_key`),
  CONSTRAINT `fk_site_configs_site` FOREIGN KEY (`site_id`) 
    REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.11 广告配置表 (ad_configs)
```sql
CREATE TABLE `ad_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `site_id` INT UNSIGNED NOT NULL,
  `position` VARCHAR(50) NOT NULL COMMENT 'header, sidebar, footer, in_article',
  `ad_type` ENUM('adsense', 'custom') NOT NULL DEFAULT 'adsense',
  `ad_code` TEXT NOT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ad_configs_unique` (`site_id`, `position`),
  KEY `idx_ad_configs_active` (`is_active`),
  CONSTRAINT `fk_ad_configs_site` FOREIGN KEY (`site_id`) 
    REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.12 翻译历史表 (translation_history)
```sql
CREATE TABLE `translation_history` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_translation_id` INT UNSIGNED NOT NULL,
  `translator_id` INT UNSIGNED,
  `original_content` LONGTEXT,
  `translated_content` LONGTEXT,
  `action` ENUM('ai_translate', 'human_edit', 'publish') NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_trans_history_article` (`article_translation_id`),
  KEY `idx_trans_history_translator` (`translator_id`),
  KEY `idx_trans_history_action` (`action`, `created_at`),
  CONSTRAINT `fk_trans_history_article` FOREIGN KEY (`article_translation_id`) 
    REFERENCES `article_translations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trans_history_translator` FOREIGN KEY (`translator_id`) 
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.13 媒体文件表 (media_files)
```sql
CREATE TABLE `media_files` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(255) NOT NULL,
  `file_path` VARCHAR(500) NOT NULL,
  `file_type` VARCHAR(50) NOT NULL,
  `file_size` INT UNSIGNED NOT NULL COMMENT '字节',
  `mime_type` VARCHAR(100) NOT NULL,
  `width` INT UNSIGNED DEFAULT NULL COMMENT '图片宽度',
  `height` INT UNSIGNED DEFAULT NULL COMMENT '图片高度',
  `alt_text` VARCHAR(255) DEFAULT NULL,
  `uploaded_by` INT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_media_files_type` (`file_type`),
  KEY `idx_media_files_uploader` (`uploaded_by`),
  KEY `idx_media_files_created` (`created_at`),
  CONSTRAINT `fk_media_files_uploader` FOREIGN KEY (`uploaded_by`) 
    REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.14 操作日志表 (audit_logs)
```sql
CREATE TABLE `audit_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` INT UNSIGNED DEFAULT NULL,
  `action` VARCHAR(50) NOT NULL,
  `resource_type` VARCHAR(50) NOT NULL,
  `resource_id` INT UNSIGNED DEFAULT NULL,
  `old_value` JSON DEFAULT NULL,
  `new_value` JSON DEFAULT NULL,
  `ip_address` VARCHAR(45) NOT NULL,
  `user_agent` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_audit_logs_user` (`user_id`),
  KEY `idx_audit_logs_action` (`action`),
  KEY `idx_audit_logs_resource` (`resource_type`, `resource_id`),
  KEY `idx_audit_logs_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 4. 索引设计策略

### 4.1 索引设计原则
1. **主键索引**：所有表都使用自增ID作为主键
2. **唯一索引**：对业务唯一字段建立唯一索引
3. **外键索引**：所有外键字段自动创建索引
4. **查询索引**：根据查询模式创建组合索引
5. **全文索引**：对需要搜索的文本字段创建全文索引

### 4.2 核心查询索引
```sql
-- 文章列表查询优化
CREATE INDEX idx_articles_list ON articles(status, published_at DESC, id);

-- 分类文章查询优化
CREATE INDEX idx_articles_category_list ON articles(category_id, status, published_at DESC);

-- 文章翻译查询优化
CREATE INDEX idx_article_trans_site ON article_translations(language_code, translation_status, published_at DESC);

-- 评论查询优化
CREATE INDEX idx_comments_article_tree ON comments(article_id, parent_id, status, created_at);

-- 标签文章查询优化
CREATE INDEX idx_article_tags_lookup ON article_tags(tag_id, article_id);
```

### 4.3 性能监控索引
```sql
-- 慢查询日志分析后添加的索引
CREATE INDEX idx_articles_hot ON articles(status, view_count DESC, published_at DESC);
CREATE INDEX idx_users_login ON users(email, password, is_active);
CREATE INDEX idx_trans_workflow ON article_translations(translation_status, language_code, created_at);
```

## 5. 数据完整性设计

### 5.1 外键约束
- 所有关联关系都建立外键约束
- 删除策略：CASCADE（级联删除）或 SET NULL（置空）
- 更新策略：RESTRICT（限制更新）

### 5.2 触发器设计
```sql
-- 更新文章评论数
DELIMITER $$
CREATE TRIGGER update_comment_count_after_insert
AFTER INSERT ON comments
FOR EACH ROW
BEGIN
  IF NEW.status = 'approved' THEN
    UPDATE articles 
    SET comment_count = comment_count + 1 
    WHERE id = NEW.article_id;
  END IF;
END$$

CREATE TRIGGER update_comment_count_after_update
AFTER UPDATE ON comments
FOR EACH ROW
BEGIN
  IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
    UPDATE articles 
    SET comment_count = comment_count + 1 
    WHERE id = NEW.article_id;
  ELSEIF OLD.status = 'approved' AND NEW.status != 'approved' THEN
    UPDATE articles 
    SET comment_count = comment_count - 1 
    WHERE id = NEW.article_id;
  END IF;
END$$
DELIMITER ;
```

### 5.3 数据验证规则
1. **邮箱格式验证**：在应用层验证
2. **URL格式验证**：在应用层验证
3. **语言代码验证**：使用ENUM限制
4. **状态值验证**：使用ENUM限制

## 6. 性能优化策略

### 6.1 分区策略
```sql
-- 评论表按月分区（针对大数据量）
ALTER TABLE comments 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202501 VALUES LESS THAN (202502),
  PARTITION p202502 VALUES LESS THAN (202503),
  PARTITION p202503 VALUES LESS THAN (202504),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 审计日志表按月分区
ALTER TABLE audit_logs 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202501 VALUES LESS THAN (202502),
  PARTITION p202502 VALUES LESS THAN (202503),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 6.2 查询优化建议
1. **避免SELECT \***：明确指定需要的字段
2. **使用LIMIT**：分页查询必须使用LIMIT
3. **避免子查询**：尽量使用JOIN替代
4. **使用EXPLAIN**：分析查询执行计划
5. **批量操作**：使用批量INSERT/UPDATE

### 6.3 缓存策略
```yaml
Redis缓存层:
  - 热门文章: ZSET结构，按浏览量排序
  - 分类树: HASH结构，缓存完整分类树
  - 文章详情: STRING结构，缓存序列化数据
  - 评论列表: LIST结构，缓存最新评论
  
缓存更新策略:
  - 写入时更新
  - 定时刷新
  - LRU淘汰
```

## 7. 数据迁移方案

### 7.1 初始化脚本
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS bengtai 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE bengtai;

-- 执行所有建表语句
SOURCE /path/to/create_tables.sql;

-- 插入初始数据
SOURCE /path/to/init_data.sql;
```

### 7.2 数据迁移步骤
1. **备份现有数据**
```bash
mysqldump -h ************ -u bengtai -p bengtai > backup_$(date +%Y%m%d).sql
```

2. **创建新表结构**
```bash
mysql -h ************ -u bengtai -p bengtai < create_tables.sql
```

3. **迁移数据**（如有旧数据）
```sql
-- 示例：迁移用户数据
INSERT INTO users_new (username, email, password, role, created_at)
SELECT username, email, password, 'editor', created_at 
FROM users_old;
```

4. **验证数据完整性**
```sql
-- 检查记录数
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'articles', COUNT(*) FROM articles
UNION ALL
SELECT 'categories', COUNT(*) FROM categories;
```

### 7.3 回滚方案
```sql
-- 保留旧表30天
RENAME TABLE users TO users_backup_20250201;
RENAME TABLE users_new TO users;

-- 如需回滚
RENAME TABLE users TO users_failed;
RENAME TABLE users_backup_20250201 TO users;
```

## 8. SQL脚本示例

### 8.1 常用查询示例
```sql
-- 获取某语言的最新文章列表
SELECT 
  a.id,
  at.title,
  at.slug,
  at.summary,
  a.featured_image,
  a.view_count,
  a.comment_count,
  a.published_at,
  c.slug as category_slug,
  ct.name as category_name
FROM articles a
INNER JOIN article_translations at ON a.id = at.article_id
INNER JOIN categories c ON a.category_id = c.id
INNER JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = at.language_code
WHERE at.language_code = 'en-US'
  AND at.translation_status = 'published'
  AND a.status = 'published'
  AND a.published_at <= NOW()
ORDER BY a.published_at DESC
LIMIT 10;

-- 获取文章详情及相关信息
SELECT 
  a.*,
  at.*,
  u.username as author_name,
  u.avatar as author_avatar
FROM articles a
INNER JOIN article_translations at ON a.id = at.article_id
INNER JOIN users u ON a.author_id = u.id
WHERE at.slug = 'cat-feeding-tips' 
  AND at.language_code = 'en-US'
  AND a.status = 'published';

-- 获取文章的评论树
WITH RECURSIVE comment_tree AS (
  SELECT 
    id, 
    article_id, 
    parent_id, 
    author_name, 
    content, 
    created_at,
    0 as level
  FROM comments
  WHERE article_id = 123 
    AND parent_id IS NULL 
    AND status = 'approved'
  
  UNION ALL
  
  SELECT 
    c.id, 
    c.article_id, 
    c.parent_id, 
    c.author_name, 
    c.content, 
    c.created_at,
    ct.level + 1
  FROM comments c
  INNER JOIN comment_tree ct ON c.parent_id = ct.id
  WHERE c.status = 'approved'
)
SELECT * FROM comment_tree ORDER BY created_at;
```

### 8.2 初始数据脚本
```sql
-- 插入默认管理员
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2b$10$...', 'super_admin');

-- 插入默认分类
INSERT INTO categories (id, parent_id, slug, sort_order) VALUES
(1, NULL, 'cats', 100),
(2, NULL, 'dogs', 200),
(3, 1, 'cat-care', 110),
(4, 1, 'cat-food', 120),
(5, 1, 'cat-behavior', 130),
(6, 2, 'dog-training', 210),
(7, 2, 'dog-care', 220),
(8, 2, 'dog-food', 230);

-- 插入分类翻译
INSERT INTO category_translations (category_id, language_code, name, description) VALUES
-- 英文
(1, 'en-US', 'Cats', 'Everything about cats'),
(2, 'en-US', 'Dogs', 'Everything about dogs'),
(3, 'en-US', 'Cat Care', 'How to take care of your cat'),
-- 德文
(1, 'de-DE', 'Katzen', 'Alles über Katzen'),
(2, 'de-DE', 'Hunde', 'Alles über Hunde'),
(3, 'de-DE', 'Katzenpflege', 'Wie Sie Ihre Katze pflegen'),
-- 俄文
(1, 'ru-RU', 'Кошки', 'Все о кошках'),
(2, 'ru-RU', 'Собаки', 'Все о собаках'),
(3, 'ru-RU', 'Уход за кошками', 'Как ухаживать за вашей кошкой');

-- 插入站点配置
INSERT INTO sites (domain, language_code, title, description) VALUES
('www.petcare.com', 'en-US', 'PetCare - Your Pet Knowledge Hub', 'Expert advice on cat and dog care'),
('www.haustiere.de', 'de-DE', 'Haustiere - Ihr Wissensportal', 'Expertenrat zur Pflege von Katzen und Hunden'),
('www.домашние-животные.рф', 'ru-RU', 'Домашние животные', 'Экспертные советы по уходу за кошками и собаками');
```

## 总结

本数据库设计充分考虑了多语言站群系统的特殊需求，通过合理的表结构设计、索引优化和数据完整性保证，确保系统能够高效、稳定地运行。

### 关键设计特点
1. **多语言分离存储**：内容表与翻译表分离，便于管理
2. **灵活的分类系统**：支持两级分类，可扩展
3. **完整的评论系统**：支持嵌套评论和审核流程
4. **站点独立配置**：每个语言站点可独立配置
5. **审计日志记录**：完整的操作记录便于追踪

### 后续优化建议
1. 根据实际数据量考虑分表分库
2. 添加读写分离支持
3. 实施定期数据归档策略
4. 建立数据库监控和告警机制