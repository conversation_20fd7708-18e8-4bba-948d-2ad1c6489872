# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/

# Production
dist/
build/
.next/
out/

# Misc
.DS_Store
*.pem
*.log

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Local env files
.env
.env*.local
.env.development
.env.test
.env.production

# IDE
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Cache
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Database
*.sqlite
*.sqlite3
*.db

# Uploads
uploads/
public/uploads/

# SSL Certificates (for local development)
*.crt
*.key

# Backup files
*.backup
*.bak

# Lock files (except package-lock.json)
yarn.lock
pnpm-lock.yaml

# PM2
.pm2/
pm2.log

# Docker
.docker/

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
.directory
.Trash-*

# JetBrains
.idea/
*.iml
*.iws
*.ipr

# Visual Studio
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
.netrwhist
*~
tags

# Project specific
/backend/uploads/
/frontend/*/dist/
/frontend/*/node_modules/
/admin/build/
.astro/