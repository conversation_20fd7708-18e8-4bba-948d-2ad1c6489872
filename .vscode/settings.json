{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "astro"], "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.turbo": true, "**/.next": true, "**/dist": true, "**/build": true, "**/.cache": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.turbo": true, "**/package-lock.json": true, "**/yarn.lock": true, "**/pnpm-lock.yaml": true}, "emmet.includeLanguages": {"astro": "html"}, "tailwindCSS.includeLanguages": {"astro": "html"}, "files.associations": {"*.css": "css", ".env*": "dotenv"}, "editor.quickSuggestions": {"strings": true}, "css.validate": false, "editor.tabSize": 2, "editor.wordWrap": "on", "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "git.enableSmartCommit": true, "git.autofetch": true, "terminal.integrated.env.osx": {"FIG_NEW_SESSION": "1"}, "terminal.integrated.env.linux": {"FIG_NEW_SESSION": "1"}}