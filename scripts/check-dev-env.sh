#!/bin/bash

# 开发环境检查脚本
# Development environment check script

echo "========================================="
echo "宠物博客站群系统 - 开发环境检查"
echo "Pet Care Blog System - Dev Environment Check"
echo "========================================="
echo ""

# 检查 Node.js
echo "1. Node.js 检查:"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "   ✅ Node.js 已安装: $NODE_VERSION"
    
    # 检查版本是否 >= 20
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1 | sed 's/v//')
    if [ "$MAJOR_VERSION" -ge 20 ]; then
        echo "   ✅ Node.js 版本满足要求 (>= 20.x)"
    else
        echo "   ⚠️  建议升级到 Node.js 20 LTS 或更高版本"
    fi
else
    echo "   ❌ Node.js 未安装"
fi
echo ""

# 检查 npm
echo "2. npm 检查:"
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo "   ✅ npm 已安装: $NPM_VERSION"
else
    echo "   ❌ npm 未安装"
fi
echo ""

# 检查 MySQL 客户端
echo "3. MySQL 客户端检查:"
if command -v mysql &> /dev/null; then
    MYSQL_VERSION=$(mysql --version | head -n 1)
    echo "   ✅ MySQL 客户端已安装:"
    echo "      $MYSQL_VERSION"
else
    echo "   ❌ MySQL 客户端未安装"
fi
echo ""

# 检查 Redis
echo "4. Redis 检查:"
if command -v redis-server &> /dev/null; then
    REDIS_VERSION=$(redis-server --version | grep -o 'v=[0-9.]*' | sed 's/v=//')
    echo "   ✅ Redis 已安装: $REDIS_VERSION"
    
    # 检查 Redis 是否运行
    if redis-cli ping &> /dev/null; then
        echo "   ✅ Redis 服务正在运行"
    else
        echo "   ⚠️  Redis 服务未运行，请启动: redis-server"
    fi
else
    echo "   ❌ Redis 未安装"
fi
echo ""

# 检查 Git
echo "5. Git 检查:"
if command -v git &> /dev/null; then
    GIT_VERSION=$(git --version)
    echo "   ✅ $GIT_VERSION"
else
    echo "   ❌ Git 未安装"
fi
echo ""

# 检查 VS Code
echo "6. VS Code 检查:"
if command -v code &> /dev/null; then
    CODE_VERSION=$(code --version | head -n 1)
    echo "   ✅ VS Code 已安装: $CODE_VERSION"
else
    echo "   ⚠️  VS Code 命令行工具未安装"
    echo "      请在 VS Code 中使用 Cmd+Shift+P 运行 'Shell Command: Install 'code' command in PATH'"
fi
echo ""

# 检查其他可选工具
echo "7. 其他开发工具:"

# Docker
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    echo "   ✅ $DOCKER_VERSION"
else
    echo "   ⚠️  Docker 未安装 (可选，用于容器化开发)"
fi

# curl
if command -v curl &> /dev/null; then
    echo "   ✅ curl 已安装"
else
    echo "   ❌ curl 未安装"
fi

# PM2
if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    echo "   ✅ PM2 已安装: $PM2_VERSION"
else
    echo "   ⚠️  PM2 未安装 (生产环境需要): npm install -g pm2"
fi

echo ""
echo "========================================="
echo "环境检查完成！"
echo "Environment check completed!"
echo "========================================="